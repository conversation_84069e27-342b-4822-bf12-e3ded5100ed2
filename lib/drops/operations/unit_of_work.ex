defmodule Drops.Operations.UnitOfWork do
  @moduledoc """
  UnitOfWork defines the processing pipeline for Operations.

  The UnitOfWork system provides a structured way to define and execute
  a series of processing steps in a specific order. Each step is defined
  as a tuple of `{module, function}` that will be called during processing.

  ## Default Pipeline

  The default pipeline consists of:

  - `:conform` - Validates input against the schema and transforms it
  - `:prepare` - Prepares the conformed parameters for validation
  - `:validate` - Validates the prepared parameters
  - `:execute` - Executes the operation with validated parameters
  - `:finalize` - Finalizes the operation result for the public API

  ## Extension Pipeline

  Extensions can inject additional steps into the pipeline by using the
  `inject_step/4` function. The pipeline order is determined dynamically
  based on the steps that are actually present in the UnitOfWork.

  ## Usage

      # Create a UnitOfWork for an operation module
      uow = UnitOfWork.new(MyOperation)

      # Process parameters through the pipeline
      case UnitOfWork.process(uow, params) do
        {:ok, result} -> # success
        {:error, error} -> # failure
      end

  """

  @type step :: atom()
  @type step_definition :: {module(), atom()}
  @type callback_type :: :before | :after | :around
  @type callback_definition :: {module(), atom(), any()}
  @type t :: %__MODULE__{
          steps: %{step() => step_definition()},
          operation_module: module(),
          callbacks: %{callback_type() => %{step() => [callback_definition()]}}
        }

  defstruct [
    :steps,
    :operation_module,
    callbacks: %{before: %{}, after: %{}, around: %{}}
  ]

  @doc """
  Creates a new UnitOfWork for the given operation module.

  The UnitOfWork will be initialized with default steps that delegate
  to the operation module itself.

  ## Parameters

  - `operation_module` - The operation module to create a UnitOfWork for

  ## Returns

  Returns a new UnitOfWork struct.
  """
  @spec new(module()) :: t()
  def new(operation_module) do
    # Start with all steps
    all_steps = %{
      conform: {operation_module, :conform},
      prepare: {operation_module, :prepare},
      validate: {operation_module, :validate},
      execute: {operation_module, :execute},
      finalize: {operation_module, :finalize}
    }

    # Remove conform step if schema is empty
    steps =
      if has_empty_schema?(operation_module) do
        Map.delete(all_steps, :conform)
      else
        all_steps
      end

    uow = %__MODULE__{
      operation_module: operation_module,
      steps: steps
    }

    # Register default after callback for execute step to wrap result in Success/Failure struct
    register_after_callback(
      uow,
      :execute,
      __MODULE__,
      :wrap_execute_result
    )
  end

  @doc """
  Injects a new step into the UnitOfWork.

  This allows extensions to add new processing steps to the pipeline.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step name to inject
  - `module` - The module containing the step function
  - `function` - The function name to call for this step

  ## Returns

  Returns the modified UnitOfWork.
  """
  @spec inject_step(t(), step(), module(), atom()) :: t()
  def inject_step(%__MODULE__{} = uow, step, module, function) do
    %{uow | steps: Map.put(uow.steps, step, {module, function})}
  end

  @doc """
  Adds a function to be called after a specific step.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step name to attach the callback to
  - `fun` - The function atom to call for this callback

  ## Returns

  Returns the modified UnitOfWork.

  ## Examples

      # Add callback after :prepare step
      uow = after_step(uow, :prepare, :my_callback)

  """
  @spec after_step(t(), step(), atom()) :: t()
  def after_step(%__MODULE__{} = uow, step, fun) when is_atom(fun) do
    register_after_callback(uow, step, uow.operation_module, fun)
  end

  @doc """
  Adds a function to be called after a specific step with custom module.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step name to attach the callback to
  - `fun` - The function atom to call for this callback
  - `mod` - The module containing the callback function

  ## Returns

  Returns the modified UnitOfWork.

  ## Examples

      # Add callback after :prepare step with custom module
      uow = after_step(uow, :prepare, :my_callback, MyModule)

  """
  @spec after_step(t(), step(), atom(), module()) :: t()
  def after_step(%__MODULE__{} = uow, step, fun, mod)
      when is_atom(fun) and is_atom(mod) do
    register_after_callback(uow, step, mod, fun)
  end

  @doc """
  Adds a function to be called before a specific step.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step name to attach the callback to
  - `fun` - The function atom to call for this callback

  ## Returns

  Returns the modified UnitOfWork.

  ## Examples

      # Add callback before :prepare step
      uow = before_step(uow, :prepare, :my_callback)

  """
  @spec before_step(t(), step(), atom()) :: t()
  def before_step(%__MODULE__{} = uow, step, fun) when is_atom(fun) do
    register_before_callback(uow, step, uow.operation_module, fun)
  end

  @doc """
  Adds a function to be called before a specific step with custom module.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step name to attach the callback to
  - `fun` - The function atom to call for this callback
  - `mod` - The module containing the callback function

  ## Returns

  Returns the modified UnitOfWork.

  ## Examples

      # Add callback before :prepare step with custom module
      uow = before_step(uow, :prepare, :my_callback, MyModule)

  """
  @spec before_step(t(), step(), atom(), module()) :: t()
  def before_step(%__MODULE__{} = uow, step, fun, mod)
      when is_atom(fun) and is_atom(mod) do
    register_before_callback(uow, step, mod, fun)
  end

  @doc """
  Registers a before callback for a specific step.

  Before callbacks are executed before the step function is called.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step name to attach the callback to
  - `module` - The module containing the callback function
  - `function` - The function name to call for this callback
  - `config` - Optional configuration data passed to the callback

  ## Returns

  Returns the modified UnitOfWork.
  """
  @spec register_before_callback(t(), step(), module(), atom(), any()) :: t()
  def register_before_callback(%__MODULE__{} = uow, step, module, function, config \\ nil) do
    callback = {module, function, config}
    before_callbacks = Map.get(uow.callbacks.before, step, [])
    updated_before = Map.put(uow.callbacks.before, step, [callback | before_callbacks])
    %{uow | callbacks: %{uow.callbacks | before: updated_before}}
  end

  @doc """
  Registers an after callback for a specific step.

  After callbacks are executed after the step function completes successfully.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step name to attach the callback to
  - `module` - The module containing the callback function
  - `function` - The function name to call for this callback
  - `config` - Optional configuration data passed to the callback

  ## Returns

  Returns the modified UnitOfWork.
  """
  @spec register_after_callback(t(), step(), module(), atom(), any()) :: t()
  def register_after_callback(%__MODULE__{} = uow, step, module, function, config \\ nil) do
    callback = {module, function, config}
    after_callbacks = Map.get(uow.callbacks.after, step, [])
    updated_after = Map.put(uow.callbacks.after, step, [callback | after_callbacks])
    %{uow | callbacks: %{uow.callbacks | after: updated_after}}
  end

  @doc """
  Registers an around callback for a specific step.

  Around callbacks wrap the step function execution and can control
  whether the step is executed and modify its result.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step name to attach the callback to
  - `module` - The module containing the callback function
  - `function` - The function name to call for this callback
  - `config` - Optional configuration data passed to the callback

  ## Returns

  Returns the modified UnitOfWork.
  """
  @spec register_around_callback(t(), step(), module(), atom(), any()) :: t()
  def register_around_callback(%__MODULE__{} = uow, step, module, function, config \\ nil) do
    callback = {module, function, config}
    around_callbacks = Map.get(uow.callbacks.around, step, [])
    updated_around = Map.put(uow.callbacks.around, step, [callback | around_callbacks])
    %{uow | callbacks: %{uow.callbacks | around: updated_around}}
  end

  @doc """
  Overrides a specific step in the UnitOfWork.

  This allows extensions to replace default implementations with their own.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step to override (:conform, :prepare, :validate, or :execute)
  - `module` - The module that contains the override function
  - `function` - The function name to call

  ## Returns

  Returns the updated UnitOfWork.
  """
  @spec override_step(t(), step(), module(), atom()) :: t()
  def override_step(%__MODULE__{} = uow, step, module, function) do
    put_in(uow.steps[step], {module, function})
  end

  @doc """
  Processes parameters through the UnitOfWork pipeline.

  This function executes all steps in the pipeline including execute and finalize.

  ## Parameters

  - `uow` - The UnitOfWork defining the pipeline
  - `context` - The context map containing params and other data

  ## Returns

  Returns `{:ok, result}` or `{:error, error}` after finalization.
  """
  @spec process(t(), map()) :: {:ok, any()} | {:error, any()}
  def process(%__MODULE__{} = uow, context) when is_map(context) do
    # Add operation_module to context so all step functions can access it
    context_with_operation = Map.put(context, :operation_module, uow.operation_module)

    # Get pipeline steps in order from the configured steps
    pipeline_steps = get_pipeline_steps(uow)

    # Process through the pipeline
    process_steps(uow, pipeline_steps, context_with_operation)
  end

  # Private functions

  def get_pipeline_steps(uow) do
    # Define the default pipeline order
    default_pipeline = [:conform, :prepare, :validate, :execute, :finalize]

    # Filter to only include steps that exist in the UnitOfWork
    available_steps = Enum.filter(default_pipeline, &Map.has_key?(uow.steps, &1))

    # Add any additional steps that were injected by extensions
    additional_steps =
      uow.steps
      |> Map.keys()
      |> Enum.reject(&(&1 in default_pipeline))

    # Simply append additional steps to the available steps
    available_steps ++ additional_steps
  end

  defp has_empty_schema?(operation_module) do
    try do
      schema = operation_module.schema()

      case schema do
        %{} -> map_size(schema) == 0
        _ -> false
      end
    rescue
      error in UndefinedFunctionError ->
        case error do
          %UndefinedFunctionError{function: :schema, arity: 0} ->
            # If schema/0 function doesn't exist, treat as empty schema
            true

          _ ->
            false
        end

      _error ->
        false
    end
  end

  defp process_steps(_uow, [], result) do
    {:ok, result}
  end

  defp process_steps(uow, [step | remaining_steps], current_context) do
    case call_step(uow, step, current_context) do
      {:ok, result} ->
        # For finalize step, return the result directly as the final output
        if step == :finalize do
          {:ok, result}
        else
          # For all other steps, use the result as the context for the next step
          process_steps(uow, remaining_steps, result)
        end

      {:error, error} ->
        {:error, error}
    end
  end

  defp call_step(uow, step, context) do
    # Execute around callbacks if any exist
    around_callbacks = Map.get(uow.callbacks.around, step, [])

    if around_callbacks != [] do
      # Execute around callbacks (they control the step execution)
      execute_around_callbacks(uow, step, context, around_callbacks)
    else
      # No around callbacks, execute step with before/after callbacks
      execute_step_with_callbacks(uow, step, context)
    end
  end

  defp execute_step_with_callbacks(uow, step, context) do
    # Execute before callbacks
    before_callbacks = Map.get(uow.callbacks.before, step, [])
    execute_before_callbacks(uow, step, context, before_callbacks)

    # Execute the actual step
    result = execute_step_function(uow, step, context)

    case result do
      {:ok, step_result} ->
        # Execute after callbacks on success and allow them to modify the result
        after_callbacks = Map.get(uow.callbacks.after, step, [])

        modified_result =
          execute_after_callbacks_with_result(
            uow,
            step,
            context,
            step_result,
            after_callbacks
          )

        {:ok, modified_result}

      {:error, _} = error ->
        error
    end
  end

  defp execute_step_function(uow, step, context) do
    {module, function} = uow.steps[step]
    apply(module, function, [context])
  end

  @doc """
  Default after callback for the execute step that wraps the result in Success/Failure structs.
  """
  def wrap_execute_result(:execute, context, execute_result, _config) do
    operation_module = Map.get(context, :operation_module)
    operation_type = operation_module.__operation_type__()
    prepared_params = Map.get(context, :prepared_params, Map.get(context, :params))

    case execute_result do
      {:ok, result} ->
        %Drops.Operations.Success{
          operation: operation_module,
          result: result,
          params: prepared_params,
          type: operation_type
        }

      {:error, error} ->
        %Drops.Operations.Failure{
          operation: operation_module,
          result: error,
          params: prepared_params,
          type: operation_type
        }

      # Handle case where execute returns raw result without tuple
      result ->
        %Drops.Operations.Success{
          operation: operation_module,
          result: result,
          params: prepared_params,
          type: operation_type
        }
    end
  end

  # Callback execution helpers

  defp execute_before_callbacks(_uow, _step, _context, []), do: :ok

  defp execute_before_callbacks(uow, step, context, [{module, function, config} | rest]) do
    apply(module, function, [step, context, config])
    execute_before_callbacks(uow, step, context, rest)
  end

  defp execute_after_callbacks_with_result(_uow, _step, _context, result, []), do: result

  defp execute_after_callbacks_with_result(uow, step, context, result, [
         {module, function, config} | rest
       ]) do
    new_result = apply(module, function, [step, context, result, config])
    execute_after_callbacks_with_result(uow, step, context, new_result, rest)
  end

  defp execute_around_callbacks(uow, step, context, [{module, function, config} | rest]) do
    next_fn = fn ->
      if rest == [] do
        execute_step_with_callbacks(uow, step, context)
      else
        execute_around_callbacks(uow, step, context, rest)
      end
    end

    apply(module, function, [step, context, next_fn, config])
  end
end
