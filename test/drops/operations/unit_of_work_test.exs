defmodule Drops.Operations.UnitOfWorkTest do
  use Drops.OperationCase, async: true

  alias Drops.Operations.UnitOfWork

  describe "new/1" do
    test "creates a UnitOfWork with default steps" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperation)

      assert uow.operation_module == TestOperation
      assert uow.steps[:conform] == {TestOperation, :conform}
      assert uow.steps[:prepare] == {TestOperation, :prepare}
      assert uow.steps[:validate] == {TestOperation, :validate}
      assert uow.steps[:execute] == {TestOperation, :execute}
      assert uow.steps[:finalize] == {TestOperation, :finalize}
    end

    test "registers default after callback for execute step" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperation)

      assert length(uow.callbacks.after[:execute]) == 1
      assert {UnitOfWork, :wrap_execute_result, nil} in uow.callbacks.after[:execute]
    end
  end

  describe "inject_step/4" do
    test "adds a new step to the UnitOfWork" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperation)

      updated_uow =
        UnitOfWork.inject_step(uow, :custom_step, TestOperation, :custom_function)

      assert updated_uow.steps[:custom_step] == {TestOperation, :custom_function}
    end
  end

  describe "after_step/3" do
    test "adds callback after a step using operation module" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperation)
      updated_uow = UnitOfWork.after_step(uow, :prepare, :my_callback)

      assert {TestOperation, :my_callback, nil} in updated_uow.callbacks.after[:prepare]
    end
  end

  describe "after_step/4" do
    test "adds callback after a step with custom module" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      defmodule CustomModule do
      end

      uow = UnitOfWork.new(TestOperation)
      updated_uow = UnitOfWork.after_step(uow, :prepare, :my_callback, CustomModule)

      assert {CustomModule, :my_callback, nil} in updated_uow.callbacks.after[:prepare]
    end
  end

  describe "before_step/3" do
    test "adds callback before a step using operation module" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperation)
      updated_uow = UnitOfWork.before_step(uow, :prepare, :my_callback)

      assert {TestOperation, :my_callback, nil} in updated_uow.callbacks.before[:prepare]
    end
  end

  describe "before_step/4" do
    test "adds callback before a step with custom module" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      defmodule CustomModule do
      end

      uow = UnitOfWork.new(TestOperation)
      updated_uow = UnitOfWork.before_step(uow, :prepare, :my_callback, CustomModule)

      assert {CustomModule, :my_callback, nil} in updated_uow.callbacks.before[:prepare]
    end
  end

  describe "register_before_callback/5" do
    test "registers a before callback with config" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperation)
      config = %{key: "value"}

      updated_uow =
        UnitOfWork.register_before_callback(
          uow,
          :prepare,
          TestOperation,
          :my_callback,
          config
        )

      assert {TestOperation, :my_callback, config} in updated_uow.callbacks.before[
               :prepare
             ]
    end
  end

  describe "register_after_callback/5" do
    test "registers an after callback with config" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperation)
      config = %{key: "value"}

      updated_uow =
        UnitOfWork.register_after_callback(
          uow,
          :prepare,
          TestOperation,
          :my_callback,
          config
        )

      assert {TestOperation, :my_callback, config} in updated_uow.callbacks.after[
               :prepare
             ]
    end
  end

  describe "register_around_callback/5" do
    test "registers an around callback with config" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperation)
      config = %{key: "value"}

      updated_uow =
        UnitOfWork.register_around_callback(
          uow,
          :prepare,
          TestOperation,
          :my_callback,
          config
        )

      assert {TestOperation, :my_callback, config} in updated_uow.callbacks.around[
               :prepare
             ]
    end
  end

  describe "override_step/4" do
    test "overrides an existing step" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command
      end

      defmodule CustomModule do
      end

      uow = UnitOfWork.new(TestOperation)
      updated_uow = UnitOfWork.override_step(uow, :prepare, CustomModule, :custom_prepare)

      assert updated_uow.steps[:prepare] == {CustomModule, :custom_prepare}
    end
  end

  describe "wrap_execute_result/4" do
    test "wraps successful result in Success struct" do
      defmodule TestOperation do
        def __operation_type__, do: :command
      end

      context = %{operation_module: TestOperation, params: %{name: "test"}}
      execute_result = {:ok, %{id: 1}}

      result = UnitOfWork.wrap_execute_result(:execute, context, execute_result, nil)

      assert %Drops.Operations.Success{
               operation: TestOperation,
               result: %{id: 1},
               params: %{name: "test"},
               type: :command
             } = result
    end

    test "wraps error result in Failure struct" do
      defmodule TestOperation do
        def __operation_type__, do: :command
      end

      context = %{operation_module: TestOperation, params: %{name: "test"}}
      execute_result = {:error, "something went wrong"}

      result = UnitOfWork.wrap_execute_result(:execute, context, execute_result, nil)

      assert %Drops.Operations.Failure{
               operation: TestOperation,
               result: "something went wrong",
               params: %{name: "test"},
               type: :command
             } = result
    end

    test "wraps raw result in Success struct" do
      defmodule TestOperation do
        def __operation_type__, do: :command
      end

      context = %{operation_module: TestOperation, params: %{name: "test"}}
      execute_result = %{id: 1}

      result = UnitOfWork.wrap_execute_result(:execute, context, execute_result, nil)

      assert %Drops.Operations.Success{
               operation: TestOperation,
               result: %{id: 1},
               params: %{name: "test"},
               type: :command
             } = result
    end
  end

  describe "process/2" do
    operation do
      schema do
        %{
          required(:name) => string()
        }
      end

      @impl true
      def execute(%{params: params}) do
        {:ok, Map.put(params, :processed, true)}
      end
    end

    test "processes through full pipeline", %{operation: operation} do
      uow = operation.__unit_of_work__()
      context = %{params: %{name: "test"}}

      {:ok, result} = UnitOfWork.process(uow, context)

      assert result == %{name: "test", processed: true}
    end

    test "adds operation_module to context" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command

        def conform(context), do: {:ok, context}
        def prepare(context), do: {:ok, context}
        def validate(context), do: {:ok, context}

        def execute(context) do
          # Verify operation_module is in context
          assert Map.has_key?(context, :operation_module)
          assert context.operation_module == TestOperation
          {:ok, context}
        end

        def finalize(result), do: {:ok, result}
      end

      uow = UnitOfWork.new(TestOperation)
      context = %{params: %{}}

      {:ok, _result} = UnitOfWork.process(uow, context)
    end

    test "handles errors in pipeline" do
      defmodule TestOperation do
        def schema, do: %{keys: []}
        def __operation_type__, do: :command

        def conform(context), do: {:ok, context}
        def prepare(context), do: {:ok, context}
        def validate(_context), do: {:error, "validation failed"}
        def execute(_context), do: {:ok, %{}}
        def finalize(result), do: {:ok, result}
      end

      uow = UnitOfWork.new(TestOperation)
      context = %{params: %{}}

      {:error, error} = UnitOfWork.process(uow, context)
      assert error == "validation failed"
    end

    test "removes conform step for empty schema" do
      defmodule TestOperationEmptySchema do
        def schema, do: %{}
        def __operation_type__, do: :command
      end

      uow = UnitOfWork.new(TestOperationEmptySchema)
      pipeline_steps = UnitOfWork.get_pipeline_steps(uow)

      refute :conform in pipeline_steps
      assert :prepare in pipeline_steps
      assert :validate in pipeline_steps
      assert :execute in pipeline_steps
      assert :finalize in pipeline_steps
    end

    test "processes correctly without conform step" do
      defmodule TestOperationNoConform do
        def schema, do: %{}
        def __operation_type__, do: :command

        def prepare(context), do: {:ok, context}
        def validate(context), do: {:ok, context}

        def execute(%{params: params}) do
          if params[:name] == nil do
            {:error, "name is required"}
          else
            {:ok, params}
          end
        end

        def finalize(%Drops.Operations.Success{result: result}) do
          {:ok, result}
        end

        def finalize(%Drops.Operations.Failure{result: result}) do
          {:error, result}
        end
      end

      uow = UnitOfWork.new(TestOperationNoConform)
      context = %{params: %{name: "Jane Doe"}}

      {:ok, result} = UnitOfWork.process(uow, context)
      assert result == %{name: "Jane Doe"}
    end
  end
end
