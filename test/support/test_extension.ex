defmodule Test.Extensions do
  defmodule PrepareExtension do
    @behaviour Drops.Operations.Extension

    @impl true
    def enabled?(_opts) do
      true
    end

    @impl true
    def extend_operation(_opts) do
      quote do
        def prepare(%{params: params} = context) do
          updated_params =
            if Map.has_key?(params, :name) do
              Map.put(params, :name, "prepared_" <> params.name)
            else
              params
            end

          {:ok, Map.put(context, :params, updated_params)}
        end

        def prepare_more(%{params: params} = context) do
          {:ok, Map.put(context, :params, Map.put(params, :prepared, true))}
        end
      end
    end

    @impl true
    def extend_unit_of_work(uow, mod, _opts) do
      Drops.Operations.UnitOfWork.inject_step(uow, :prepare_more, mod, :prepare_more)
    end
  end

  defmodule ValidateExtension do
    @behaviour Drops.Operations.Extension

    @impl true
    def enabled?(_opts) do
      true
    end

    @impl true
    def extend_operation(_opts) do
      quote do
        def validate(%{params: params} = context) do
          if Map.has_key?(params, :name) and String.contains?(params.name, "invalid") do
            {:error, "name cannot contain 'invalid'"}
          else
            {:ok, context}
          end
        end
      end
    end
  end

  defmodule CallbackExtension do
    @behaviour Drops.Operations.Extension

    @impl true
    def enabled?(_opts) do
      true
    end

    @impl true
    def extend_operation(_opts) do
      quote do
        def log_before_prepare(:prepare, context, _config) do
          # Before callbacks are for side effects - store in process dictionary for testing
          Process.put(:before_prepare_called, true)
          :ok
        end

        def log_after_prepare(:prepare, context, result, _config) do
          # After callbacks can modify the result
          case result do
            {:ok, result_context} ->
              # Add markers from both before and after callbacks
              updated_context =
                result_context
                |> Map.put(:after_prepare_called, true)
                |> Map.put(
                  :before_prepare_called,
                  Process.get(:before_prepare_called, false)
                )

              {:ok, updated_context}

            other ->
              other
          end
        end
      end
    end

    @impl true
    def extend_unit_of_work(uow, _mod, _opts) do
      uow
      |> Drops.Operations.UnitOfWork.before_step(:prepare, :log_before_prepare)
      |> Drops.Operations.UnitOfWork.after_step(:prepare, :log_after_prepare)
    end
  end
end
